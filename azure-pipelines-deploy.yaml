parameters:
  - name: packageLocationWeb
    type: string
    default: 'drop/PharmaLex.VigiLit.Web.zip'
  - name: packageLocationFunction
    type: string
    default: 'drop/VigiLitImportApp.zip'
  - name: environments
    type: object
    default:
      - name: 'dev'
        azureSubscription: 'Vigilit_Development'
        DailyReferenceClassificationEmailTemplateId: 'd-cbb1271e7c624dd299a3032f44bf73f9'
        InvitationEmailTemplateId: 'd-f73d79c558e74f86a354de3deee629b9'
        CaseEmailTemplateId: 'd-90ecabd7bad948a9b1cb1e20e0904f92'
        dependsOn:
      - name: 'int'
        azureSubscription: 'Vigilit_Development'
        DailyReferenceClassificationEmailTemplateId: 'd-84d820b5c93a41a8b034fa9f3f07d29a'
        InvitationEmailTemplateId: 'd-ac37e90b938a4a26a0e65ee9e0246d09'
        CaseEmailTemplateId: 'd-9b42682dc7d140358c37da658d9de270'
        dependsOn:
          - Dev
#      - name: 'auto'
#        azureSubscription: 'Vigilit_Development'
#        DailyReferenceClassificationEmailTemplateId: 'd-d8a5d86e12b64067bff324aee6a919c2'
#        InvitationEmailTemplateId: 'd-341d77d46c6f424db24557138930d355'
#        CaseEmailTemplateId: 'd-ccbd65886b5c42c5a2d9030f4a197398'
#        dependsOn:
#          - Dev
      - name: 'stg'
        azureSubscription: 'Vigilit_Development'
        DailyReferenceClassificationEmailTemplateId: 'd-aab2273d7f394f5b85c93dec5b8d42ea'
        InvitationEmailTemplateId: 'd-57d90e4af4fc438b91fde29ce77daea1'
        CaseEmailTemplateId: 'd-e8ad9aa295884ebeab24ee2ae3eed3eb'
        dependsOn:
          - int
      - name: 'prod'
        locked: true
        azureSubscription: 'Vigilit_Production'
        DailyReferenceClassificationEmailTemplateId: 'd-ce0a18d13c6b4b92a32b0416b1c88cd6'
        InvitationEmailTemplateId: 'd-e77a5ac6d84b423fb69940d360bf3081'
        CaseEmailTemplateId: 'd-4f248887c35c41ddb8a7df1708dd016e'
        dependsOn:
          - stg

pool: 'pv-pool'

trigger: none

resources:
  pipelines:
    - pipeline: build-pipeline
      source: VigiLit.Build
      project: VigiLit
      trigger:
        branches:
          include:
            - develop
  repositories:
  - repository: PlaywrightVigilit
    type: git
    name: PlaywrightVigilit

variables:
  nameOfPlatform: 'vgt'
  region: 'eun' # to be specified per env if/when we spread out of EUN

stages:
  - ${{ each env in parameters.Environments }}:
      - stage: '${{ env.name }}'
        displayName: '${{ env.name }} Deploy'
        dependsOn: '${{ env.dependsOn }}'
        variables:
          AppSettings.BuildInfo: 'Build: $(resources.pipeline.build-pipeline.runName)'
          AppSettings.BuildNumber: $(resources.pipeline.build-pipeline.runName)
          AppSettings.EnvironmentName: '${{ env.name }}'
          KeyVaultName: ${{ variables.nameOfPlatform }}-${{ env.name }}-kv-${{ variables.region }}
          AzureStorage.CaseDocumentUpload.AccountName: '${{ variables.nameOfPlatform }}${{ env.name }}public${{ variables.region }}'
          AzureStorage.CaseDocument.AccountName: '${{ variables.nameOfPlatform }}${{ env.name }}shared${{ variables.region }}'
          AzureStorage.TrackingSheetDocument.AccountName: '${{ variables.nameOfPlatform }}${{ env.name }}shared${{ variables.region }}'
          AzureStorage.ImportFileDocumentUpload.AccountName: '${{ variables.nameOfPlatform }}${{ env.name }}shared${{ variables.region }}'
          AzureStorage.ImportFileDocument.AccountName: '${{ variables.nameOfPlatform }}${{ env.name }}shared${{ variables.region }}'
          AzureStorage.ImportFileScrapeDocument.AccountName: '${{ variables.nameOfPlatform }}${{ env.name }}shared${{ variables.region }}'
          EmailSettings.DailyReferenceClassificationEmailTemplateId: ${{ env.DailyReferenceClassificationEmailTemplateId}}
          EmailSettings.InvitationEmailTemplateId: ${{ env.InvitationEmailTemplateId}}
          EmailSettings.CaseEmailTemplateId: ${{ env.CaseEmailTemplateId}}
          MessageBus.AzureServiceBus.Namespace: '${{ variables.nameOfPlatform }}-${{ env.name }}-servicebus-${{ variables.region }}'
          PhlexVision.ConsumerKeyHeaderValue: 'vigilit'
          ${{ if eq(env.name, 'prod') }}:
            EmailSettings.WebsiteUri: 'https://vigilit.smartphlex.com/'
            PhlexVision.CallbackBaseUrl: 'https://vigilit.smartphlex.com/api/PhlexVisionApi/'
          ${{ else }}:
            EmailSettings.WebsiteUri: 'https://vigilit-${{ env.name }}.smartphlex.com/'
            PhlexVision.CallbackBaseUrl: 'https://vigilit-${{ env.name }}.smartphlex.com/api/PhlexVisionApi/'
        jobs:
          - deployment: DeploySQL
            displayName: 'Deploy SQL dacpac ${{ env.name }}'
            environment: ${{ env.name }}
            pool: 'pv-windows-pool'
            strategy:
              runOnce:
                deploy:
                  steps:
                    - task: DownloadPipelineArtifact@2
                      inputs:
                        source: 'specific'
                        project: $(resources.pipeline.build-pipeline.projectID)
                        pipeline: $(resources.pipeline.build-pipeline.pipelineName)
                        runVersion: 'specific'
                        runBranch: $(resources.pipeline.build-pipeline.sourceBranch)
                        runId: $(resources.pipeline.build-pipeline.runID)
                        path: '$(System.DefaultWorkingDirectory)'
                    - task: AzureKeyVault@2
                      displayName: 'Azure Key Vault: ${{ variables.nameOfPlatform }}-${{ env.name }}-kv-${{ variables.region }}'
                      inputs:
                        azureSubscription: ${{ env.azureSubscription }}
                        KeyVaultName: ${{ variables.nameOfPlatform }}-${{ env.name }}-kv-${{ variables.region }}
                        RunAsPreJob: true
                    - task: SqlAzureDacpacDeployment@1
                      displayName: 'Azure SQL SqlTask'
                      inputs:
                        azureSubscription: ${{ env.azureSubscription }}
                        AuthenticationType: servicePrincipal
                        ServerName: '${{ variables.nameOfPlatform }}-${{ env.name }}-sqlserver-${{ variables.region }}.database.windows.net'
                        DatabaseName: '${{ variables.nameOfPlatform }}-${{ env.name }}-default-${{ variables.region }}'
                        deployType: SqlTask
                        SqlFile: '$(System.DefaultWorkingDirectory)/drop/Migrations/migration.sql'
                        IpDetectionMethod: 'AutoDetect'
          - deployment: Deploy
            displayName: 'Deploy Env: ${{ env.name }}'
            environment: ${{ env.name }}
            dependsOn: DeploySQL
            strategy:
              runOnce:
                deploy:
                  steps:
                    - checkout: none
                    - task: DownloadPipelineArtifact@2
                      inputs:
                        source: 'specific'
                        project: $(resources.pipeline.build-pipeline.projectID)
                        pipeline: $(resources.pipeline.build-pipeline.pipelineName)
                        runVersion: 'specific'
                        runBranch: $(resources.pipeline.build-pipeline.sourceBranch)
                        runId: $(resources.pipeline.build-pipeline.runID)
                        path: '$(System.DefaultWorkingDirectory)'
                    - task: AzureKeyVault@2
                      displayName: 'Azure Key Vault: ${{ variables.nameOfPlatform }}-${{ env.name }}-kv-${{ variables.region }}'
                      inputs:
                        azureSubscription: ${{ env.azureSubscription }}
                        KeyVaultName: ${{ variables.nameOfPlatform }}-${{ env.name }}-kv-${{ variables.region }}
                        RunAsPreJob: true
                    - task: FileTransform@1
                      displayName: 'File Transform: WebApp'
                      inputs:
                        folderPath: '$(System.DefaultWorkingDirectory)/${{ parameters.packageLocationWeb }}'
                        fileType: json
                        targetFiles: '**/appsettings.json'
                    - task: FileTransform@1
                      displayName: 'File Transform: FunctionApp'
                      inputs:
                        folderPath: '$(System.DefaultWorkingDirectory)/${{ parameters.packageLocationFunction }}'
                        fileType: json
                        targetFiles: '**/appSettings.json'
                    - task: AzureRmWebAppDeployment@4
                      displayName: Azure App Service Deploy
                      inputs:
                        ConnectionType: 'AzureRM'
                        azureSubscription: ${{ env.azureSubscription }}
                        ResourceGroupName: 'rg-${{ variables.nameOfPlatform }}-${{ env.name }}-${{ variables.region }}'
                        appType: 'webApp'
                        WebAppName: '${{ variables.nameOfPlatform }}-${{ env.name }}-as-${{ variables.region }}'
                        packageForLinux: '$(System.DefaultWorkingDirectory)/${{ parameters.packageLocationWeb }}'
                    - task: AzureFunctionApp@2
                      displayName: Azure Function App Deploy
                      inputs:
                        azureSubscription: ${{ env.azureSubscription }}
                        appType: functionApp
                        appName: ${{ variables.nameOfPlatform }}-${{ env.name }}-fa-${{ variables.region }}
                        package: '$(System.DefaultWorkingDirectory)/${{ parameters.packageLocationFunction }}'