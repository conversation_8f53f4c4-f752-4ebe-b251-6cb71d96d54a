﻿using PharmaLex.VigiLit.Ui.ViewModels.Countries;

namespace PharmaLex.VigiLit.ContractManagement.Ui.Journals;

public interface IJournalService
{
    /// <summary>
    /// Asynchronously retrieves all journals.
    /// </summary>
    /// <returns>
    /// A task that represents the asynchronous operation. The task result contains an 
    /// <see cref="IEnumerable{T}"/> of <see cref="JournalViewModel"/>.
    /// </returns>
    Task<IEnumerable<JournalViewModel>> GetAll();

    /// <summary>
    /// Returns a list of journals that are linked to a particular country
    /// </summary>
    /// <param name="countryId">Country id</param>
    /// <returns>List of JournalModels</returns>
    Task<IEnumerable<JournalViewModel>> GetJournalsForCountry(int countryId);

    /// <summary>
    /// Gets the countries for which there are enabled journals.
    /// </summary>
    /// <returns></returns>
    Task<IEnumerable<CountryModel>> GetSubscribedCountries();

    /// <summary>
    /// Saves new journal
    /// </summary>
    /// <param name="model">Journal model</param>
    /// <returns>Task</returns>
    Task AddAsync(JournalModel model);

    /// <summary>
    /// Retrieves a journal by its unique identifier.
    /// </summary>
    /// <param name="id">The unique identifier of the journal to retrieve.</param>
    /// <returns>
    /// A task that represents the asynchronous operation. 
    /// The task result contains the <see cref="JournalViewModel"/> for the specified journal.
    /// </returns>
    /// <remarks>
    /// This method fetches the journal data from the data source and maps it to a view model 
    /// for presentation or editing purposes.
    /// </remarks>
    Task<JournalViewModel> GetJournal(int id);

    /// <summary>
    /// Updates an existing journal entry in the data store with the provided journal model.
    /// </summary>
    /// <param name="model">The journal model containing updated values. The <c>Id</c> property must be set to identify the journal to update.</param>
    /// <returns>A task representing the asynchronous update operation.</returns>
    /// <exception cref="ArgumentException">Thrown when the <c>Id</c> is null or invalid.</exception>
    Task UpdateAsync(JournalModel model);

    /// <summary>
    /// Retrieves the count of contracts that include the specified journal
    /// and where the journal is enabled.
    /// </summary>
    /// <param name="id">The ID of the journal to check.</param>
    /// <returns>
    /// A task representing the asynchronous operation. The task result contains
    /// the count of contract versions referencing the enabled journal with the specified ID.
    /// </returns>
    Task<int> CountContractsWithEnabledJournal(int id);
}