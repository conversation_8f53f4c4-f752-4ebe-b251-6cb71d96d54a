﻿using AutoMapper;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.Countries;


namespace PharmaLex.VigiLit.ContractManagement.Ui.Journals;

internal class JournalService(IJournalRepository journalRepository, IMapper mapper) : IJournalService
{
    public async Task<IEnumerable<JournalViewModel>> GetAll()
    {
        var journals = await journalRepository.GetAll();

        foreach (var journal in journals)
        {
            journal.Issn = journal.Issn ?? "N/A";
        }

        return mapper.Map<IEnumerable<JournalViewModel>>(journals);

    }
    public async Task<JournalViewModel> GetJournal(int id)
    {
        var journal = await journalRepository.GetById(id);
        return mapper.Map<JournalViewModel>(journal);
    }

    public async Task<IEnumerable<JournalViewModel>> GetJournalsForCountry(int countryId)
    {
        var journals = await journalRepository.GetForCountry(countryId);
        return mapper.Map<IEnumerable<JournalViewModel>>(journals);
    }

    public async Task<IEnumerable<CountryModel>> GetSubscribedCountries()
    {
        var countries = await journalRepository.GetSubscribedCountries();
        return mapper.Map<IEnumerable<CountryModel>>(countries);
    }

    public async Task AddAsync(JournalModel model)
    {
        var journal = new Journal { Name = model.Name, CountryId = model.CountryId, Url = model.Url, Enabled = model.Enabled, Issn = model.Issn};
        journalRepository.Add(journal);

        await journalRepository.SaveChangesAsync();
    }

    public async Task UpdateAsync(JournalModel model)
    {
        if (!model.Id.HasValue)
        {
            throw new ArgumentException("Id is required to update a journal");
        }

        var journal = await journalRepository.GetById(model.Id.Value);
        mapper.Map(model, journal);
        await journalRepository.SaveChangesAsync();
    }

    public async Task<int> CountContractsWithEnabledJournal(int id)
    {
        return await journalRepository.GetContractsWithEnabledJournalById(id);
    }
}