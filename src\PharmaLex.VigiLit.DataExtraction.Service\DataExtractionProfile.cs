﻿using AutoMapper;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.ImportManagement.Client.Dto;

namespace PharmaLex.VigiLit.DataExtraction.Service;

internal class DataExtractionProfile : Profile
{
    public static ReferenceDto Copy(ExtractedReference source)
    {
        ArgumentNullException.ThrowIfNull(source);

        var destination = new ReferenceDto()
        {
            Abstract = source.Abstract.Value,
            AffiliationTextFirstAuthor = source.Affiliations[0].Value,
            Authors = string.Join(",", source.Authors.Select(a => a.Value).ToArray()),
            CountryOfOccurrence = source.CountryOfOccurrence.Value,
            Doi = source.Doi.Value,
            FullPagination = source.Pages.Value,
            Issn = source.Issn.Value,
            Language = "LANGUAGE",
            SourceSystem = (int)SourceSystem.Web,
            SourceId = "SOURCE ID",
            PublicationType = "PUBLICATION TYPE",
            PublicationYear = ushort.TryParse(source.Year?.Value, out var year) ? year : null,
            Title = source.Title.Value,
            Volume = source.Volume.Value,
            VolumeAbbreviation = "VOLUME ABBREVIATION",
            Keywords = source.Keywords.Value,
            MeshHeadings = "MESH HEADINGS",
            JournalTitle = source.JournalTitle.Value,
        };
        return destination;
    }
    public static FailedImportReferenceDto CopyFailed(ExtractedReference source)
    {
        ArgumentNullException.ThrowIfNull(source);

        var destination = new FailedImportReferenceDto()
        {
            Abstract = source.Abstract.Value,
            AffiliationTextFirstAuthor = source.Affiliations[0].Value,
            Authors = string.Join(",", source.Authors.Select(a => a.Value).ToArray()),
            CountryOfOccurrence = source.CountryOfOccurrence.Value,
            Doi = source.Doi.Value,
            FullPagination = source.Pages.Value,
            Issn = source.Issn.Value,
            Language = "LANGUAGE",
            SourceSystem = (int)SourceSystem.Web,
            SourceId = "SOURCE ID",
            PublicationType = "PUBLICATION TYPE",
            PublicationYear = ushort.TryParse(source.Year?.Value, out var year) ? year : null,
            Title = source.Title.Value,
            Volume = source.Volume.Value,
            VolumeAbbreviation = "VOLUME ABBREVIATION",
            Keywords = source.Keywords.Value,
            MeshHeadings = "MESH HEADINGS",
            JournalTitle = source.JournalTitle.Value,

            AbstractConfidence = ToByteOrDefault(source.Abstract?.Value),
            AffiliationTextFirstAuthorConfidence = ToByteOrDefault(source.Affiliations?.FirstOrDefault()?.Value),
            AuthorsConfidence = ToByteOrDefault(source.Authors?.FirstOrDefault()?.Value),
            CountryOfOccurrenceConfidence = ToByteOrDefault(source.CountryOfOccurrence?.Value),
            DoiConfidence = ToByteOrDefault(source.Doi?.Value),
            FullPaginationConfidence = ToByteOrDefault(source.Pages?.Value),
            IssnConfidence = ToByteOrDefault(source.Issn?.Value),
            IssueConfidence = ToByteOrDefault(source.IssueNumber?.Value),
            PublicationYearConfidence = ToByteOrDefault(source.Year?.Value),
            TitleConfidence = ToByteOrDefault(source.Title?.Value),
            VolumeConfidence = ToByteOrDefault(source.Volume?.Value),
            KeywordsConfidence = ToByteOrDefault(source.Keywords?.Value),
            JournalTitleConfidence = ToByteOrDefault(source.JournalTitle?.Value),
            //DateRevisedConfidence = 0,// NOSONAR
            //LanguageConfidence = 0,// NOSONAR
            //SourceSystemConfidence = 0,// NOSONAR
            //VolumeAbbreviationConfidence = 0;// NOSONAR
        };
        return destination;
    }

    private static byte ToByteOrDefault(string? input) =>
    byte.TryParse(input, out var result) ? result : (byte)0;
}