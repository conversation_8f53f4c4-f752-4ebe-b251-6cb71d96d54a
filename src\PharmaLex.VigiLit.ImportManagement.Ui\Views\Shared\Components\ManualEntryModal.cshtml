﻿<script type="text/x-template" id="manual-entry-modal-template">
    <div id="manual-entry-modal" v-cloak>
        <div class="modal fade" :id="modalId" tabindex="-1" role="dialog">
            <div class="modal-dialog  modal-wide" role="document">
                <div class="modal-content" v-cloak>
                    <div class="modal-header d-flex justify-content-between align-items-center">
                        <h2 class="modal-title mb-0 flex-grow-1">{{ modalTitle }}</h2>
                        <a v-if="modalId === 'failedImportModal'"
                           href="#"
                           @@click.prevent="onDownload"
                           class="text-sm text-primary d-inline-flex align-items-center gap-1"
                           title="Download sample template"
                           style="line-height: 1; vertical-align: middle;">
                            <i v-if="manualEntryObj.filename" class="m-icon" style="font-size: 18px; line-height: 1;">link</i>
                            <span style="line-height: 1;">{{ manualEntryObj.filename }}</span>
                        </a>
                    </div>
                    <div class="modal-body">
                        <div id="ad-hoc-manual-entry-form">
                            <section>
                                <input type="hidden" v-model="manualEntryObj.id" />
                                <div v-if="errors" class="error-summary-panel">
                                        Some fields have missing data
                                </div>
                                <div class="form-group">
                                    <label for="doi">Check DOI</label>
                                    <div class="input-group flex">
                                        <i class="m-icon pl-1 z-index-1" style="position: absolute; left: 0; top: 30px;">search</i>
                                        <input type="search"
                                               v-model="manualEntryObj.doi"
                                               placeholder="DOI"
                                               class="pl-4 white-background" />
                                        <button type="button" class="button ml-1 medium" @@click="checkDoi">Check</button>
                                    </div>
                                    <div id="doi-invalid-message" v-if="doiInvalid">
                                        <p>Please enter valid DOI.</p>
                                    </div>
                                    <div id="doi-exist-message" v-if="doiExists">
                                        <p>DOI Already Exists.</p>
                                    </div>
                                </div>
                                <div class="flex">
                                    <div class="flex-item">
                                        <div class="form-group">
                                            <label for="title" :class="{ 'error-label': errors.title }">Article Title</label>
                                            <input type="text" v-model.trim="manualEntryObj.title"
                                                   @@input="validateForm" :class="{ 'error-field-highlight': errors.title }"/>
                                            <label v-if="errors.title" class="error-message">{{ errors.title }}</label>
                                        </div>
                                        <div class="form-group">
                                            <label for="authors">Authors</label>
                                            <input type="text" v-model.trim="manualEntryObj.authors" />
                                        </div>
                                        <div class="form-group">
                                            <label for="journalTitle" :class="{ 'error-label': errors.journalTitle }">Journal Title</label>

                                            <input type="text" v-model.trim="manualEntryObj.journalTitle"
                                                   @@input="validateForm" :class="{ 'error-field-highlight': errors.journalTitle }" />

                                            <label v-if="errors.journalTitle" class="error-message">{{ errors.journalTitle }}</label>
                                        </div>
                                        <div class="form-group">
                                            <label for="volume">Volume</label>
                                            <input type="text" v-model.trim="manualEntryObj.volume" />
                                        </div>
                                        <div class="form-group">
                                            <label for="issue">Issue Number</label>
                                            <input type="text" v-model.trim="manualEntryObj.issue" />
                                        </div>

                                        <div class="form-group">
                                            <label for="fullPagination">Page/Pages</label>
                                            <input type="text" v-model.trim="manualEntryObj.fullPagination" />
                                        </div>

                                        <div class="form-group">
                                            <label for="publicationYear">Publication Year</label>
                                            <input type="number" maxlength="4" v-model.number="manualEntryObj.publicationYear" />
                                        </div>

                                        <div class="form-group">
                                            <label for="issn">ISSN</label>
                                            <input type="text" v-model.trim="manualEntryObj.issn" />
                                        </div>
                                        <div class="form-group">
                                            <label for="countryOfOccurrence" :class="{ 'error-label': errors.countryOfOccurrence }">Country of Occurrence</label>
                                            <select v-model="manualEntryObj.countryOfOccurrence" @@change="validateForm" :class="{ 'error-field-highlight': errors.countryOfOccurrence }">
                                                <option disabled value="">Select a country</option>                                                
                                                <option v-for="c in countries" :key="c" :value="c">{{ c }}</option>
                                            </select>
                                            <label v-if="errors.countryOfOccurrence" class="error-message">{{ errors.countryOfOccurrence }}</label>
                                        </div>
                                        <div class="form-group">
                                            <label for="language">Language</label>
                                            <input type="text" v-model.trim="manualEntryObj.language" />
                                        </div>

                                        <div class="form-group">
                                            <label for="keywords">Keywords</label>
                                            <input type="text" v-model.trim="manualEntryObj.keywords" />
                                        </div>

                                    </div>
                                    <div class="flex-item">
                                        <div class="form-group">
                                            <label for="abstract" :class="{ 'error-label': errors.abstract }">Abstract</label>
                                            <textarea v-model.trim="manualEntryObj.abstract" rows="10" @@input="validateForm" :class="{ 'error-field-highlight': errors.abstract }"></textarea>
                                            <label v-if="errors.abstract" class="error-message">{{ errors.abstract }}</label>
                                        </div>
                                    </div>
                                </div>
                            </section>
                        </div>

                        <div class="modal-footer">
                            <button type="button" id="cancelBtn" class="button secondary icon-button-cancel btn-default" data-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-default" :disabled="isFormInvalid || isFailedImportFile" @@click="handleSubmit()">
                                {{ isEdit ? 'Save' : 'Create' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</script>

<script type="text/javascript">
    vueApp.component('manual-entry-modal', {
        template: '#manual-entry-modal-template',
        props: {
            model: Object,
            countries: Array,
            modalId: {
                type: String,
                required: true
            }
        },
        data() {
            return {
                manualEntryObj: { ...this.model },
                doiInvalid: false,
                doiExists: false,
                errors: {},
                sourceSystem: {
                    manual: 2
                },
                statusType: {
                    new: 5
                }
            };
        },
        computed: {
            isEdit() {
                return this.manualEntryObj.id && this.manualEntryObj.id !== "0";
            },
            isFormInvalid() {
                return Object.values(this.errors).some(err => err);
            },
            modalTitle() {
                return this.modalId === 'manualEntryModal'
                    ? 'Manual Entry'
                    : 'Manual Correction';
            },
            // Temporary till we are able to edit FailedImportFile 
            isFailedImportFile() {
                return this.modalId === 'failedImportModal' ? true : false;
            }
        },
        watch: {
            model: {
                handler(newVal) {
                    this.manualEntryObj = { ...newVal };
                    this.validateForm();
                },
                deep: true
            },
            'manualEntryObj.doi'(newVal) {
                if (!newVal || newVal.trim() === '') {
                    this.doiInvalid = false;
                    this.doiExists = false;
                }
            }
        },
        methods: {
            async checkDoi() {
                this.doiInvalid = false;
                this.doiExists = false;

                const doiRegex = /^10\.\d{4,9}\/[-._;()/:A-Z0-9]+$/i;
                if (!doiRegex.test(this.manualEntryObj.doi)) {
                    this.doiInvalid = true;
                    return;
                }
                // To be implemented
                // For 'failedImportFile' modal, check if the DOI exists in the failedImportTable before submitting
                try {
                    const validRes = await fetch(`/Import/CheckIfDoiIsValid?doi=${this.manualEntryObj.doi}`);
                    const isValid = await validRes.json();
                    this.doiInvalid = !isValid;

                    if (isValid) {
                        const existRes = await fetch(`/Import/CheckIfDoiExist?doi=${this.manualEntryObj.doi}&id=${this.manualEntryObj.id}`);
                        const exists = await existRes.json();
                        this.doiExists = exists;
                    }
                } catch (e) {
                    this.$emit('toast', { message: 'DOI check failed.', type: 'error' });
                }
            },
            async submit(payload, url) {
                try {
                    const res = await fetch(url, {
                        method: "POST",
                        credentials: "same-origin",
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        },
                        body: JSON.stringify(payload)
                    });

                    const text = await res.text();
                    let data = {};

                    try {
                        data = text ? JSON.parse(text) : {};
                    } catch (err) {
                        console.warn('Failed to parse JSON:', err);
                    }

                    if (res.status === 400) {
                        if (data.errors) {
                            const errorMessages = [];
                            for (const field in data.errors) {
                                const messages = Array.isArray(data.errors[field])
                                    ? data.errors[field]
                                    : [data.errors[field]];
                                messages.forEach(msg => errorMessages.push(`* ${msg}`));
                            }
                            plx.toast.show(errorMessages.join('\n'), 2, 'failed', null, 2500);
                        } else {
                            plx.toast.show('Validation failed, but no details provided.', 2, 'failed', null, 2500);
                        }
                        throw new Error('Validation failed');
                    }

                    if (!res.ok) {
                        throw new Error(`Server error: ${res.status}`);
                    }

                    plx.toast.show(
                        this.isEdit
                            ? "Import reference edited successfully"
                            : "Import reference created successfully",
                        2,
                        "confirm",
                        null,
                        2500
                    );

                    this.hideModal();
                    location.reload();

                } catch (error) {
                    console.error('Fetch error:', error);
                    if (error.message !== 'Validation failed') {
                        plx.toast.show('Something went wrong, please try again.', 2, 'failed', null, 2500);
                    }
                }
            },
            handleSubmit() {
                this.validateForm();
                if (this.isFormInvalid) return;
                let url = '';
                const payload = {
                    id: this.manualEntryObj.id || 0,
                    abstract: this.manualEntryObj.abstract?.trim() || '',
                    affiliationTextFirstAuthor: this.manualEntryObj.affiliationTextFirstAuthor?.trim() || '',
                    authors: this.manualEntryObj.authors?.trim() || '',
                    countryOfOccurrence: this.manualEntryObj.countryOfOccurrence?.trim() || '',
                    doi: this.manualEntryObj.doi?.trim() || '',
                    fullPagination: this.manualEntryObj.fullPagination?.trim() || '',
                    issn: this.manualEntryObj.issn?.trim() || '',
                    issue: this.manualEntryObj.issue?.trim() || '',
                    language: this.manualEntryObj.language?.trim() || '',
                    sourceSystem: this.sourceSystem.manual, 
                    publicationType: this.manualEntryObj.publicationType?.trim() || '',
                    publicationYear: (
                        this.manualEntryObj.publicationYear === '' ||
                        this.manualEntryObj.publicationYear === null ||
                        isNaN(this.manualEntryObj.publicationYear)
                    ) ? null : parseInt(this.manualEntryObj.publicationYear),
                    title: this.manualEntryObj.title?.trim() || '',
                    volume: this.manualEntryObj.volume?.trim() || '',
                    keywords: this.manualEntryObj.keywords?.trim() || '',
                    journalTitle: this.manualEntryObj.journalTitle?.trim() || '',
                    statusType: this.statusType.new 
                };

                if (this.modalId === 'manualEntryModal') {
                    url = this.isEdit
                        ? '/Import/UpdateImportReference'
                        : '/Import/CreateImportReference';
                } else if (this.modalId === 'failedImportModal') {
                    url = '/Import/UpdateFailedImportFile';
                }
                this.submit(payload, url);
            },
            validateForm() {
                this.errors.title = (this.manualEntryObj.title || '').trim()
                    ? ''
                    : '* Article title is required.';

                this.errors.journalTitle = (this.manualEntryObj.journalTitle || '').trim()
                    ? ''
                    : '* Journal Title is required.';

                this.errors.abstract = (this.manualEntryObj.abstract || '').trim()
                    ? ''
                    : '* Abstract is required.';

                this.errors.countryOfOccurrence = (this.manualEntryObj.countryOfOccurrence || '').trim()
                    ? ''
                    : '* Country is required.';
            },
            onDownload() {
                const { batchId, filename } = this.manualEntryObj;

                if (!batchId || !filename) {
                    plx.toast.show('Batch ID or File Name is missing.', 2, 'failed', null, 2000);
                    return;
                }

                const url = `/Import/DownloadFailedImportFile/${batchId}/${filename}`;

                const onFailure = function (xhr) {
                    let errorMessage = 'Downloading file failed.';

                    if (xhr && xhr.responseText) {
                        try {
                            const error = JSON.parse(xhr.responseText);
                            if (error && error.message) {
                                errorMessage = error.message;
                            }
                        } catch (e) {
                            console.log(e);
                        }
                    }
                    plx.toast.show(errorMessage, 2, 'failed', null, 2000);
                };

                const onComplete = function (file) {
                    plx.toast.show(`File "${file.filename}" downloaded successfully.`, 5, 'confirm', null, 2500);
                };

                DownloadFile.fromUrl(url, null, null, onFailure, onComplete);
            },
            parseErrors(serverErrors) {
                for (let key in serverErrors) {
                    this.errors[key] = serverErrors[key].errors[0].errorMessage;
                }
            },
            showModal() {
                const modalSelector = `#${this.modalId}`;
                $(modalSelector).modal('show');
            },
            hideModal() {
                const modalSelector = `#${this.modalId}`;
                $(modalSelector).modal('hide');
            },

            resetManualEntryObj() {
                this.manualEntryObj = {
                    id: 0,
                    abstract: '',
                    affiliationTextFirstAuthor: '',
                    authors: '',
                    countryOfOccurrence: '',
                    doi: '',
                    fullPagination: '',
                    issn: '',
                    issue: '',
                    language: '',
                    sourceSystem: this.sourceSystem.manual, 
                    publicationType: '',
                    publicationYear: null,
                    title: '',
                    volume: '',
                    keywords: '',
                    journalTitle: '',
                    statusType: this.statusType.new  
                };
                this.doiExists = false;
                this.doiInvalid = false;
                this.validateForm();
            }

        },
        mounted() {
            this.$nextTick(() => {
                const modalSelector = `#${this.modalId}`;
                const $modal = $(modalSelector);

                if ($modal.length) {
                    $modal.on('shown.bs.modal', () => {
                        this.validateForm();
                    });

                    $modal.on('hidden.bs.modal', () => {
                        this.resetManualEntryObj();
                    });
                } else {
                    console.warn(`Modal with ID '${this.modalId}' not found in DOM.`);
                }
            });
        }
    });
</script>