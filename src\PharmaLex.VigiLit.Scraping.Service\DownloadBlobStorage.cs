﻿using Azure.Storage.Blobs.Models;
using Azure.Storage.Blobs;
using PharmaLex.BlobStorage.Descriptors;
using PharmaLex.BlobStorage.Interfaces;
using PharmaLex.VigiLit.Scraping.Service.Import;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service;

public class DownloadBlobStorage : IDownloadBlobStorage
{
    private readonly IDocumentService _documentService;
    private readonly AzureStorageImportScrapeDocumentOptions _importScrapeDocumentOptions;
    private readonly BlobServiceClient _blobServiceClient;
    private string _folderName;

    public DownloadBlobStorage(
        IDocumentService documentService, 
        AzureStorageImportScrapeDocumentOptions importScrapeDocumentOptions,
        BlobServiceClient blobServiceClient)
    {
        _documentService = documentService;
        _folderName = string.Empty;
        _importScrapeDocumentOptions = importScrapeDocumentOptions;
        _blobServiceClient = blobServiceClient;
    }

    public async Task WriteDataItemAsync(string fileName, Stream fileStream, CancellationToken cancellationToken = default)
    {
        var documentDescriptor = GetDocumentDescriptor(fileName);
        await _documentService.Create(documentDescriptor, fileStream, cancellationToken);
    }

    public void SetBlobFolderName(string folderName)
    {
        _folderName = folderName;
    }

    public async Task<IEnumerable<string>> GetBlobPathsAsync(string folderName, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(folderName))
            throw new ArgumentNullException(nameof(folderName));

        var containerClient = _blobServiceClient.GetBlobContainerClient(_importScrapeDocumentOptions.ContainerName);
        var prefix = $"scraping/{folderName}/"; 

        var blobPaths = new List<string>();

        await foreach (BlobItem blobItem in containerClient.GetBlobsAsync(
            prefix: prefix,
            cancellationToken: cancellationToken))
        {
            // Get the full blob path (e.g., "scraping/myfolder/file1.pdf")
            blobPaths.Add(blobItem.Name);
        }

        return blobPaths;
    }

    private DocumentDescriptor GetDocumentDescriptor(string fileName)
    {
        var blobName = $"scraping/{_folderName}/{fileName}";
        return new DocumentDescriptor(_importScrapeDocumentOptions.AccountName, _importScrapeDocumentOptions.ContainerName, blobName);
    }
}
