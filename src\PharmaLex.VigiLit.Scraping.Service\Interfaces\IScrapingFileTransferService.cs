namespace PharmaLex.VigiLit.Scraping.Service.Interfaces;

/// <summary>
/// Service for transferring files from scraping container to import upload container
/// </summary>
public interface IScrapingFileTransferService
{
    /// <summary>
    /// Copies files from scraping container to import upload container
    /// </summary>
    /// <param name="actorTaskId">The Apify actor task ID (used as folder name in scraping container)</param>
    /// <param name="batchId">The batch ID to use in the import upload container</param>
    /// <param name="filePaths">List of file paths in the scraping container</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of file names that were successfully copied</returns>
    Task<IEnumerable<string>> CopyScrapingFilesToImportUpload(
        string actorTaskId, 
        Guid batchId, 
        IEnumerable<string> filePaths, 
        CancellationToken cancellationToken = default);
}
