﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\PharmaLex.VigiLit.DataExtraction.Client\PharmaLex.VigiLit.DataExtraction.Client.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Logging\PharmaLex.VigiLit.Logging.csproj" />
  </ItemGroup>

  <ItemGroup>
	<PackageReference Include="AutoMapper" Version="14.0.0" />
	<PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
	<PackageReference Include="PharmaLex.BlobStorage" Version="8.0.0.126" />
	<PackageReference Include="Phlex.Core.Apify" Version="1.0.0.247" />
	<PackageReference Include="Phlex.Core.Apify.Webhook" Version="1.0.0.247" />
  </ItemGroup>

</Project>
