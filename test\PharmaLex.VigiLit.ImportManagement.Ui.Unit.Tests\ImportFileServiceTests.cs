﻿using AutoMapper;
using Moq;
using PharmaLex.BlobStorage.Descriptors;
using PharmaLex.VigiLit.Application.AutoMapper;
using PharmaLex.VigiLit.DataExtraction.Client;
using PharmaLex.VigiLit.ImportManagement.Documents;
using PharmaLex.VigiLit.ImportManagement.Entities;
using PharmaLex.VigiLit.ImportManagement.Ui.FileImport;
using PharmaLex.VigiLit.ImportManagement.Ui.Models;
using PharmaLex.VigiLit.Test.Fakes.Entities;
using System.Globalization;
using System.Text;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Unit.Tests;

public class ImportFileServiceTests
{
    private readonly IMapper _mapper;
    private readonly IImportFileService _importFileService;

    private readonly Mock<IImportFileRepository> _importFileRepository = new();
    private readonly Mock<IImportFileDocumentService> _importFileDocumentUploadService = new();
    private readonly Mock<IDataExtractionClient> _mockDataExtractionClient = new();

    public ImportFileServiceTests()
    {

        var mapperConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new ImportReferenceMappingProfile());
            mc.AddProfile(new ImportFileMappingProfile());
            mc.AddProfile(new ManualEntryMappingProfile());
            mc.AddProfile(new FailedImportMappingProfile());
        });

        _mapper = mapperConfig.CreateMapper();

        _importFileService = new ImportFileService(
            _importFileRepository.Object,
            _importFileDocumentUploadService.Object,
            _mapper,
            _mockDataExtractionClient.Object);
    }

    [Fact]
    public async Task GetImportFilesByBatchId_ShouldReturnImportFileModels_WhenFilesExist()
    {
        // Arrange
        var batchId = Guid.NewGuid();
        var fileName1 = "file1.txt";
        var fileName2 = "file2.txt";

        var importFiles = new List<FakeImportFile>
        {
            new FakeImportFile(1) { FileName = fileName1, BatchId = batchId, FileSize = 1024,FileHash = "5d41402abc4b2a76b9719d911017c592"},
            new FakeImportFile(2) { FileName = fileName2, BatchId = batchId, FileSize = 2048,FileHash = "6d41402abc4b2a76b9719d911017c592"}
        };

        var expectedImportFiles = new List<ImportFileModel>
        {
            new ImportFileModel(fileName1, batchId,"5d41402abc4b2a76b9719d911017c592") { Id = 1, FileName = fileName1, BatchId = batchId, FileSize = 1024,FileHash = "5d41402abc4b2a76b9719d911017c592"},
            new ImportFileModel(fileName2, batchId,"6d41402abc4b2a76b9719d911017c592" ) { Id = 2, FileName = fileName2, BatchId = batchId, FileSize = 2048,FileHash =  "6d41402abc4b2a76b9719d911017c592"}
        };

        _importFileRepository
            .Setup(repo => repo.GetImportFilesByBatchId(batchId))
            .ReturnsAsync(importFiles);


        // Act
        var result = await _importFileService.GetImportFile(batchId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Equal(expectedImportFiles[0].FileName, result[0].FileName);
        Assert.Equal(expectedImportFiles[1].FileName, result[1].FileName);
        _importFileRepository.Verify(repo => repo.GetImportFilesByBatchId(batchId), Times.Once);
    }

    [Fact]
    public async Task GetImportCards_ReturnsImportDisplayCard_WhenMultipleFilesInSameBatch()
    {
        // Arrange
        var batchId = Guid.NewGuid();
        var fileName1 = "file1.txt";
        var fileName2 = "file2.txt";

        var importFiles = new List<FakeImportFile>
        {
            new FakeImportFile(1) { FileName = fileName1, BatchId = batchId, FileSize = 1024, CreatedDate = GetDateTime("13/03/2025 11:31:56"),LastUpdatedDate = GetDateTime("13/03/2025 11:31:56"),CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>",FileHash = "5d41402abc4b2a76b9719d911017c592"},
            new FakeImportFile(2) { FileName = fileName2, BatchId = batchId, FileSize = 2048, CreatedDate = GetDateTime("13/03/2025 11:31:56"),LastUpdatedDate = GetDateTime("08/04/2025 11:31:56"),CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>",FileHash = "6d41402abc4b2a76b9719d911017c592"},
            new FakeImportFile(1) {FileName = fileName1, BatchId = batchId, FileSize = 1024, CreatedDate = GetDateTime("02/03/2025 11:31:56"), LastUpdatedDate = GetDateTime("12/03/2025 11:31:56"), CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>", FileHash = "7d41402abc4b2a76b9719d911017c592"},
            new FakeImportFile(2) {FileName = fileName2, BatchId = batchId, FileSize = 2048, CreatedDate = GetDateTime("08/03/2025 11:31:56"), LastUpdatedDate = GetDateTime("18/03/2025 11:31:56"), CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>", FileHash = "8d41402abc4b2a76b9719d911017c592"}
        };

        var expectedImportFiles = new ImportDisplayCard
        {
            Id = batchId.ToString(),
            ImportType = "File",
            Title = "",
            Filename = "",
            DateFrom = "",
            DateTo = "",
            FileCount = 4,
            CreatedBy = "<EMAIL>",
            LastUpdatedBy = "<EMAIL>",
        };

        _importFileRepository
            .Setup(repo => repo.GetAll())
            .ReturnsAsync(importFiles);


        //// Act
        var result = await _importFileService.GetCards();

        //// Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(expectedImportFiles.FileCount, result[0].FileCount);
        Assert.Equal(expectedImportFiles.CreatedBy, result[0].CreatedBy);
        Assert.Equal(expectedImportFiles.LastUpdatedBy, result[0].LastUpdatedBy);
    }

    [Fact]
    public async Task CreateImportFile_ShouldUploadSuccessfully()
    {
        // Arrange
        var batchId = Guid.NewGuid();
        var fileName = "test.pdf";
        var stream = new MemoryStream();
        var model = new ImportFileUploadModel
        {
            ImportFile = new ImportFileModel(fileName, batchId, "6d41402abc4b2a76b9719d911017c592")
            {
                FileName = fileName,
                BatchId = batchId,
                FileHash = "6d41402abc4b2a76b9719d911017c592"
            },
            Stream = stream
        };

        var importFile = new ImportFile { BatchId = batchId, FileName = fileName, FileHash = "5d41402abc4b2a76b9719d911017c592" };


        _importFileRepository.Setup(repo => repo.Add(importFile));
        _importFileRepository.Setup(repo => repo.SaveChangesAsync());

        // Mock Upload File Document
        _importFileDocumentUploadService
           .Setup(service => service.Create(
                It.IsAny<ImportFileUploadDescriptor>(),
                It.IsAny<Stream>(),
                It.IsAny<CancellationToken>()
            )).Returns(Task.CompletedTask);

        _importFileDocumentUploadService
            .Setup(service => service.GetDocumentProperties(
                It.IsAny<ImportFileUploadDescriptor>(),
                It.IsAny<CancellationToken>())
            )
            .ReturnsAsync(new DocumentProperties(fileName, 1024, "https://example.com/file.pdf"));

        // Act
        await _importFileService.Add(model);

        // Assert

        _importFileRepository.Verify(x => x.Add(It.Is<ImportFile>(x =>
                x.BatchId == batchId
                && x.FileName == fileName
            )), Times.Once());
        _importFileRepository.Verify(repo => repo.SaveChangesAsync(), Times.Exactly(2));
        _importFileDocumentUploadService.Verify(service => service.Create(
                It.IsAny<ImportFileUploadDescriptor>(),
                It.IsAny<Stream>(),
                It.IsAny<CancellationToken>()
            ), Times.Once);

        var documentPropertiesReturned = await _importFileDocumentUploadService.Object.GetDocumentProperties(
            It.IsAny<ImportFileUploadDescriptor>(),
            It.IsAny<CancellationToken>()
        );

        Assert.NotNull(documentPropertiesReturned);
        Assert.Equal(fileName, documentPropertiesReturned.FileName);
        Assert.Equal(1024, documentPropertiesReturned.FileSize);
        Assert.Equal("https://example.com/file.pdf", documentPropertiesReturned.Uri);
    }

    [Fact]
    public async Task DeleteImportFiles_ValidBatchId_ShouldDeleteImportFiles()
    {
        // Arrange
        var batchId = Guid.NewGuid();
        var importFiles = new List<ImportFile>
        {
            new ImportFile { FileName = "file1.pdf", BatchId = batchId,FileHash = "5d41402abc4b2a76b9719d911017c592"},
            new ImportFile { FileName = "file2.pdf", BatchId = batchId , FileHash = "6d41402abc4b2a76b9719d911017c592"}
        };

        _importFileRepository.Setup(repo => repo.GetImportFilesByBatchId(batchId))
            .ReturnsAsync(importFiles);

        _importFileRepository.Setup(repo => repo.Remove(It.IsAny<ImportFile>()));
        _importFileRepository.Setup(repo => repo.SaveChangesAsync());

        _importFileDocumentUploadService.Setup(service => service.Delete(It.IsAny<ImportFileUploadDescriptor>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _importFileDocumentUploadService
            .Setup(service => service.Exists(It.IsAny<ImportFileUploadDescriptor>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        await _importFileService.Abandon(batchId);

        // Assert
        _importFileRepository.Verify(repo => repo.GetImportFilesByBatchId(batchId), Times.Once);
        _importFileRepository.Verify(repo => repo.Remove(It.IsAny<ImportFile>()), Times.Exactly(2));
        _importFileRepository.Verify(repo => repo.SaveChangesAsync(), Times.Once);

        _importFileDocumentUploadService.Verify(service => service.Delete(It.IsAny<ImportFileUploadDescriptor>(), It.IsAny<CancellationToken>()), Times.Exactly(2)); // One for each file
    }

    [Fact]
    public async Task DeleteImportFile_ShouldRemoveFile_WhenFileIsValid()
    {
        var importFile = CreateImportFileModel();

        var fileEntity = new ImportFile { FileName = importFile.FileName, BatchId = importFile.BatchId, FileHash = "5d41402abc4b2a76b9719d911017c592" };

        _importFileRepository
            .Setup(repo => repo.GetImportFile(importFile.FileName, importFile.BatchId))
            .ReturnsAsync(fileEntity);

        _importFileRepository.Setup(r => r.Remove(It.IsAny<ImportFile>()));
        _importFileRepository.Setup(r => r.SaveChangesAsync()).ReturnsAsync(1);
        _importFileDocumentUploadService.Setup(service => service.Delete(It.IsAny<ImportFileUploadDescriptor>(), It.IsAny<CancellationToken>()))
             .Returns(Task.CompletedTask);

        _importFileDocumentUploadService
            .Setup(service => service.Exists(It.IsAny<ImportFileUploadDescriptor>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        await _importFileService.DeleteImportFile(importFile);

        // Assert
        _importFileRepository.Verify(r => r.Remove(It.Is<ImportFile>(f => f.FileName == importFile.FileName && f.BatchId == importFile.BatchId)), Times.Once);
        _importFileRepository.Verify(r => r.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task DeleteImportFile_ShouldThrowInvalidOperationException_WhenRepositoryFails()
    {
        // Arrange
        var importFile = CreateImportFileModel();

        var fileEntity = new ImportFile { FileName = importFile.FileName, BatchId = importFile.BatchId, FileHash = "5d41402abc4b2a76b9719d911017c592" };

        _importFileRepository
            .Setup(repo => repo.GetImportFile(importFile.FileName, importFile.BatchId))
            .ReturnsAsync(fileEntity);

        _importFileRepository
                .Setup(repo => repo.Remove(fileEntity))
                  .Throws(new Exception("Database error"));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _importFileService.DeleteImportFile(importFile)
        );

        Assert.Equal($"deleting {importFile.FileName} failed.", exception.Message);
    }

    [Fact]
    public async Task DeleteImportFile_ShouldThrowKeyNotFoundException_WhenFileNotFound()
    {
        // Arrange
        var importFile = CreateImportFileModel();

        _importFileRepository
            .Setup(repo => repo.GetImportFile(importFile.FileName, importFile.BatchId))
            .ThrowsAsync(new KeyNotFoundException("Import file not found"));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<KeyNotFoundException>(() =>
            _importFileService.DeleteImportFile(importFile));

        Assert.Equal("Import file not found", exception.Message);
    }

    [Fact]
    public async Task DeleteImportFiles_ShouldNotThrow_WhenAllFilesAreDeletedSuccessfully()
    {
        // Arrange
        var batchId = Guid.NewGuid();
        var fileName1 = "test1.pdf";
        var fileName2 = "test2.pdf";

        var importFiles = new List<ImportFileModel>
        {
            new ImportFileModel(fileName1, batchId,"6d41402abc4b2a76b9719d911017c592") { FileName=fileName1,BatchId = batchId, FileSize = 2048, FileHash = "6d41402abc4b2a76b9719d911017c592"},
            new ImportFileModel(fileName2, batchId,"7d41402abc4b2a76b9719d911017c592") { FileName=fileName1,BatchId = batchId, FileSize = 1048,FileHash = "7d41402abc4b2a76b9719d911017c592"}
        };

        var fileEntities = new List<ImportFile>
        {
            new ImportFile { FileName =fileName1, BatchId = batchId , FileHash = "5d41402abc4b2a76b9719d911017c592"},
            new ImportFile { FileName =fileName2, BatchId = batchId , FileHash = "6d41402abc4b2a76b9719d911017c592"}
        };

        _importFileRepository
            .Setup(repo => repo.GetImportFile(It.IsAny<string>(), It.IsAny<Guid>()))
            .ReturnsAsync((string? fileName, Guid batchId) =>
                fileEntities.FirstOrDefault(f => f.FileName == fileName && f.BatchId == batchId)!);

        _importFileDocumentUploadService
            .Setup(service => service.Exists(It.IsAny<ImportFileUploadDescriptor>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        _importFileDocumentUploadService
            .Setup(service => service.Delete(It.IsAny<ImportFileUploadDescriptor>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _importFileRepository
            .Setup(repo => repo.Remove(It.IsAny<ImportFile>()))
            .Verifiable();

        _importFileRepository
            .Setup(repo => repo.SaveChangesAsync())
            .ReturnsAsync(1);

        // Act
        await _importFileService.DeleteImportFiles(importFiles);

        // Assert
        _importFileDocumentUploadService.Verify(service => service.Delete(It.IsAny<ImportFileUploadDescriptor>(), It.IsAny<CancellationToken>()), Times.Exactly(importFiles.Count));
        _importFileRepository.Verify(repo => repo.Remove(It.IsAny<ImportFile>()), Times.Exactly(importFiles.Count));
    }

    [Fact]
    public async Task Download_Returns_DownloadFile()
    {
        // Arrange
        var batchId = Guid.NewGuid();
        var fileName1 = "test1.pdf";
        byte[] bytes = Encoding.ASCII.GetBytes("my stream content");
        using var stream = new MemoryStream(bytes);

        _importFileDocumentUploadService
            .Setup(x => x.OpenRead(It.IsAny<ImportFileUploadDescriptor>(), default))
            .ReturnsAsync(stream);

        // Act
        var downloadFile = await _importFileService.DownloadImportedFile(batchId, fileName1);

        //// Assert
        Assert.NotNull(downloadFile);
        Assert.Equal(fileName1, downloadFile.FileName);
        Assert.Equal("application/pdf", downloadFile.ContentType);
        Assert.True(bytes.SequenceEqual(downloadFile.Bytes));
    }


    private static DateTime GetDateTime(string dateText)
    {
        var dateTime = DateTime.ParseExact(dateText, "dd/MM/yyyy HH:mm:ss", CultureInfo.InvariantCulture);
        return dateTime;
    }

    private static ImportFileModel CreateImportFileModel()
    {
        var batchId = Guid.NewGuid();
        var fileName = "test.pdf";

        return new ImportFileModel(fileName, batchId, "6d41402abc4b2a76b9719d911017c592")
        {
            FileName = fileName,
            BatchId = batchId,
            FileSize = 2048,
            FileHash = "6d41402abc4b2a76b9719d911017c592"
        };
    }
}


